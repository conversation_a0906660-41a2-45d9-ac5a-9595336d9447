// ==UserScript==
// @name         Linux DO 屏蔽
// @description  过滤 Linux DO 网站上的关键词和用户
// <AUTHOR>
// @version      1.4
// @match        http*://linux.do/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
  "use strict";

  // ========== 屏蔽功能样式 ==========
  GM_addStyle(`
        /* ========== 屏蔽功能样式 ========== */
        .filter-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            z-index: 9999;
        }
        .filter-modal {
            display: none;
            position: fixed;
            bottom: 70px;
            right: 20px;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.2);
            z-index: 9999;
            width: 300px;
        }
        .filter-input {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .filter-list {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 10px;
        }
        .filter-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .remove-keyword {
            color: red;
            cursor: pointer;
        }
        .filtered-topic {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
            position: absolute !important;
            z-index: -9999 !important;
            transform: scale(0) !important;
            max-height: 0 !important;
            max-width: 0 !important;
            overflow: hidden !important;
        }


        /* 屏蔽按钮和触发区域样式 */
        .block-list-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            font-size: 13px !important;
            z-index: 1000;
            padding: 6px 12px;
            background: #000;
            box-shadow: 0 0 6px rgba(0, 0, 0, 0.2);
            color: #fff;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            pointer-events: none;
            transition: opacity 0.3s ease;
            font-weight: bold;
        }

        .trigger-area {
            position: fixed;
            bottom: 0;
            right: 0;
            width: 100px;
            height: 100px;
            z-index: 999;
        }

        .trigger-area:hover .block-list-button {
            opacity: 1;
            pointer-events: auto;
        }

        .block-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-75%, -50%);
            width: 500px;
            max-width: 90vw;
            height: auto;
            max-height: 80vh;
            background: rgba(255, 255, 255, 0.5);
            padding: 20px;
            border-radius: 16px;
            box-shadow: 0 1px 50px rgba(0, 0, 0, 0.3);
            z-index: 1001;
            overflow: auto;
        }

        .block-dialog::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            z-index: -1;
            backdrop-filter: blur(50px);
        }

        .block-title-bar {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: none;
            cursor: move;
            user-select: none;
        }

        .block-close-button {
            position: absolute;
            top: 12px;
            right: 12px;
            width: 16px;
            height: 16px;
            background: black;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.15s ease;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            text-indent: -9999px;
            padding: 0;
            z-index: 1002;
        }

        .block-close-button:hover {
            background: red;
        }

        .block-close-line {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 2px;
            background: white;
            transition: background 0.15s ease;
        }

        .block-close-line-1 {
            transform: translate(-50%, -50%) rotate(45deg);
        }

        .block-close-line-2 {
            transform: translate(-50%, -50%) rotate(-45deg);
        }

        .block-list {
            margin: 20px 0 0 0;
            max-height: 220px;
            font-size: 12px;
            padding: 10px;
            background: #00000012;
            border-radius: 12px;
            color: #333;
            overflow-y: auto;
        }

        .block-keyword-list {
            margin: 15px 0 0 0;
            max-height: 100px;
        }

        .block-category-list {
            margin: 15px 0 0 0;
            max-height: 100px;
        }

        .block-input-container {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            margin: 15px 0 !important;
            width: 100% !important;
        }

        .block-input {
            flex: none !important;
            border: 1px solid #ddd !important;
            background: #ffffff96 !important;
            border-radius: 8px !important;
            font-size: 13px !important;
            padding: 5px 10px !important;
            width: 180px !important;
            margin: 10px !important;
            box-sizing: border-box !important;
        }

        .block-input:focus {
            border: 1px solid #999 !important;
            box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1) !important;
            outline: none !important;
        }

        .block-button {
            flex: none;
            border-radius: 8px;
            border: 0px solid black;
            background: #000;
            padding: 7px 12px;
            color: white;
            font-size: 13px;
            cursor: pointer;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
            white-space: nowrap;
            text-align: center;
        }

        .block-item {
            display: inline-block;
            margin: 0 5px 5px 0;
            border-radius: 4px;
        }

        .block-dialog h3 {
            margin: 0 0 10px;
            font-size: 14px;
            color: #000;
        }

        /* 头像容器样式 */
        .avatar-container {
            position: relative; /* 为绝对定位的按钮提供相对定位容器 */
            display: inline-block; /* 或 block，取决于布局 */
            transition: margin-bottom 0.2s ease; /* 平滑过渡效果 */
            z-index: 10; /* 确保容器在其他元素之上 */
        }

        /* 头像下方的屏蔽按钮样式 */
        .block-avatar-hover-button {
            display: none !important; /* 默认隐藏，使用!important确保优先级 */
            position: absolute;
            left: 50%; /* 水平居中 */
            transform: translateX(-50%); /* 水平居中对齐 */
            padding: 4px 8px; /* 增加按钮大小，更容易点击 */
            font-size: 11px; /* 增大字体 */
            background-color: rgba(0, 0, 0, 0.9); /* 更深的黑色背景 */
            color: #fff; /* 白色文字 */
            border-radius: 6px;
            cursor: pointer;
            z-index: 1000; /* 确保在其他元素之上 */
            box-shadow: 0px 2px 4px rgba(0,0,0,0.3);
            white-space: nowrap; /* 防止按钮文字换行 */
            opacity: 0; /* 初始透明 */
            bottom: -32px;
            transition: opacity 0.2s ease, transform 0.1s ease; /* 更快的动画效果 */
            pointer-events: auto; /* 确保可以点击 */
        }
        /* 创建一个隐形的扩展区域，使鼠标从头像移动到按钮时不会触发按钮消失 */
        .avatar-container::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: -30px; /* 扩展到按钮下方 */
            height: 30px; /* 足够高以覆盖按钮区域 */
            background: transparent; /* 完全透明 */
            z-index: 99; /* 低于按钮和楼主标签 */
        }

        /* 当鼠标悬停在头像容器上时显示按钮 */
        .avatar-container:hover .block-avatar-hover-button {
            display: inline-block !important; /* 在容器悬停时显示，使用!important确保优先级 */
            opacity: 1 !important; /* 完全不透明 */
        }

        /* 当鼠标悬停在按钮上时保持显示并添加效果 */
        .block-avatar-hover-button:hover {
            opacity: 1 !important; /* 确保按钮保持可见 */
            background-color: rgba(0, 0, 0, 1) !important; /* 更深的黑色 */
            box-shadow: 0px 2px 6px rgba(0,0,0,0.4) !important; /* 增强阴影 */
            transform: translateX(-50%) scale(1.05) !important; /* 轻微放大效果 */
        }

    `);

  // ========== 屏蔽功能模块 ==========
  // #region 屏蔽功能模块

  // 安全的存储操作函数
  function safeGetValue(key, defaultValue) {
    try {
      return GM_getValue(key, defaultValue) || defaultValue;
    } catch (error) {
      console.error(`Error getting value for ${key}:`, error);
      return defaultValue;
    }
  }

  function safeSetValue(key, value) {
    try {
      GM_setValue(key, value);
      return true;
    } catch (error) {
      console.error(`Error setting value for ${key}:`, error);
      showNotification(`保存失败: ${error.message}`);
      return false;
    }
  }

  // 获取存储的屏蔽列表
  let keywords = safeGetValue("filterKeywords", []);
  let blockedUsers = safeGetValue("filterUsers", []);
  let blockedCategories = safeGetValue("filterCategories", []);

  // 重新加载屏蔽列表
  function reloadBlockLists() {
    keywords = safeGetValue("filterKeywords", []);
    blockedUsers = safeGetValue("filterUsers", []);
    blockedCategories = safeGetValue("filterCategories", []);
  }

  // 显示通知函数
  function showNotification(message, duration = 3000) {
    let notification = document.querySelector(".notification");

    if (!notification) {
      notification = document.createElement("div");
      notification.className = "notification";
      notification.style.cssText = `
				position: fixed;
				bottom: 60px;
				right: 20px;
				background: rgba(0, 0, 0, 0.8);
				color: white;
				padding: 10px 15px;
				border-radius: 5px;
				font-size: 13px;
				z-index: 1002;
				opacity: 0;
				transition: opacity 0.3s ease;
				pointer-events: none;
			`;
      document.body.appendChild(notification);
    }

    notification.textContent = message;
    notification.style.opacity = "1";

    setTimeout(() => {
      notification.style.opacity = "0";
    }, duration);
  }

  // 检查jQuery是否加载
  function checkJQuery(callback, maxAttempts = 10) {
    if (maxAttempts <= 0) {
      console.error("jQuery加载失败");
      return;
    }

    if (typeof jQuery !== "undefined") {
      callback();
    } else {
      setTimeout(() => checkJQuery(callback, maxAttempts - 1), 200);
    }
  }

  // 防抖函数
  function debounce(func, wait) {
    let timeout;
    return function (...args) {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  // 初始化
  /**
   * 初始化主逻辑
   */
  function init() {
    // console.log('初始化屏蔽脚本...');

    // 仅在帖子详情页执行重构逻辑
    if (window.location.pathname.startsWith("/t/")) {
      // console.log('检测到帖子详情页，准备执行重构');
      renderUI();
      addEventListeners();

      // 立即执行一次过滤和头像处理
      // 增加延迟，确保美化脚本已经完成DOM修改
      setTimeout(() => {
        filterPosts();
        addBlockUserButtonsToAvatars(); // 添加头像旁的屏蔽按钮
      }, 1000);

      // 监听DOM变化，处理动态加载的内容（包括懒加载）
      const observer = new MutationObserver(
        debounce((mutations) => {
          // 检查是否有新的帖子元素被添加
          const hasNewPosts = mutations.some((mutation) => mutation.type === "childList" && mutation.addedNodes.length > 0 && Array.from(mutation.addedNodes).some((node) => node.nodeType === 1 && (node.classList?.contains("topic-post") || node.querySelector?.(".topic-post"))));

          if (hasNewPosts) {
            console.log(`🔄 [DEBUG] 检测到新帖子加载，执行过滤`);
            filterPosts();
            addBlockUserButtonsToAvatars();
          }
        }, 200) // 减少防抖时间，提高响应速度
      );

      // 配置观察器：观察更深层的变化以捕获懒加载内容
      const targetNode = document.body.querySelector(".topic-posts") || document.body;
      observer.observe(targetNode, {
        childList: true,
        subtree: true, // 观察深层变化以捕获懒加载
        attributes: false,
        characterData: false,
      });
    } else {
      // console.log('非帖子详情页，仅执行基础UI渲染和过滤');
      // 对于非帖子页面，可能只需要按钮和列表页过滤
      renderUI(); // 仍然显示按钮
      addEventListeners();

      // 增加延迟，确保美化脚本已经完成DOM修改
      setTimeout(() => {
        filterPosts(); // 列表页过滤
        addBlockUserButtonsToAvatars(); // 添加头像旁的屏蔽按钮
      }, 1000);

      // 列表页也可能需要监听器，以处理动态加载
      const listObserver = new MutationObserver(
        debounce((mutations) => {
          console.log(`🔄 [DEBUG] 列表页DOM变化触发`, {
            mutationsCount: mutations.length,
            timestamp: new Date().toISOString(),
            mutations: mutations.map((m) => ({
              type: m.type,
              target: m.target.tagName + (m.target.className ? "." + m.target.className : ""),
              addedNodes: m.addedNodes.length,
              removedNodes: m.removedNodes.length,
            })),
          });
          filterPosts();
          addBlockUserButtonsToAvatars(); // 添加头像旁的屏蔽按钮
        }, 300)
      );

      listObserver.observe(document.body, { childList: true, subtree: true });
    }

    // 专门针对懒加载内容的检测机制
    let lastPostCount = 0;
    setInterval(() => {
      const currentPostCount = document.querySelectorAll(".topic-post").length;
      if (currentPostCount > lastPostCount) {
        console.log(`🔄 [DEBUG] 检测到懒加载新帖子: ${lastPostCount} -> ${currentPostCount}`);
        lastPostCount = currentPostCount;
        // 立即执行过滤，处理新加载的内容
        filterPosts();
        addBlockUserButtonsToAvatars();
      } else {
        lastPostCount = currentPostCount;
      }
    }, 1000); // 每2秒检查一次

    // 监听滚动事件，懒加载通常在滚动时触发
    let scrollTimeout;
    window.addEventListener("scroll", () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        const newPostCount = document.querySelectorAll(".topic-post").length;
        if (newPostCount > lastPostCount) {
          console.log(`📜 [DEBUG] 滚动触发懒加载: ${lastPostCount} -> ${newPostCount}`);
          lastPostCount = newPostCount;
          filterPosts();
          addBlockUserButtonsToAvatars();
        }
      }, 500); // 滚动停止500ms后检查
    });
  }

  // 确保jQuery加载后再初始化
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", () => checkJQuery(init));
  } else {
    checkJQuery(init);
  }

  // 创建触发区域和按钮
  function createBlockButton() {
    const triggerArea = document.createElement("div");
    triggerArea.className = "trigger-area";

    const button = document.createElement("button");
    button.innerText = "屏蔽列表";
    button.className = "block-list-button";
    button.addEventListener("click", showBlockListDialog);

    triggerArea.appendChild(button);
    document.body.appendChild(triggerArea);
  }

  // 显示屏蔽列表对话框
  function showBlockListDialog() {
    // 检查是否已存在对话框
    if (document.querySelector(".block-dialog")) {
      return;
    }

    const dialog = document.createElement("div");
    dialog.className = "block-dialog";

    // 创建标题栏作为拖动区域
    const titleBar = document.createElement("div");
    titleBar.className = "block-title-bar";

    // 拖动实现
    let isDragging = false;
    let startX, startY;
    let initialLeft, initialTop;

    function startDragging(e) {
      if (e.target === titleBar) {
        isDragging = true;
        startX = e.clientX;
        startY = e.clientY;

        const rect = dialog.getBoundingClientRect();
        initialLeft = rect.left;
        initialTop = rect.top;

        dialog.style.transform = "none";
        dialog.style.left = `${initialLeft}px`;
        dialog.style.top = `${initialTop}px`;
      }
    }

    function doDrag(e) {
      if (!isDragging) return;

      e.preventDefault();
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      dialog.style.left = `${initialLeft + deltaX}px`;
      dialog.style.top = `${initialTop + deltaY}px`;
    }

    function stopDragging() {
      isDragging = false;
    }

    titleBar.addEventListener("mousedown", startDragging);
    document.addEventListener("mousemove", doDrag);
    document.addEventListener("mouseup", stopDragging);

    // 创建关闭按钮
    const closeButton = document.createElement("button");
    closeButton.className = "block-close-button";
    closeButton.innerHTML = '<span class="block-close-line block-close-line-1"></span><span class="block-close-line block-close-line-2"></span>';
    closeButton.onclick = () => {
      document.removeEventListener("mousemove", doDrag);
      document.removeEventListener("mouseup", stopDragging);
      document.body.removeChild(dialog);
    };

    // 渲染用户列表为带样式的项目
    function renderUserList(users) {
      // 获取最近的30个用户名
      const recentUsers = users.slice(-30);
      if (recentUsers.length === 0) {
        return "<div style='font-style:italic;color:#999'>暂无屏蔽用户</div>";
      }

      return recentUsers.map((user) => `<span class="block-item">${user}</span>`).join(" ");
    }

    // 渲染关键词列表为带样式的项目
    function renderKeywordList(keywords) {
      if (keywords.length === 0) {
        return "<div style='font-style:italic;color:#999'>暂无屏蔽关键词</div>";
      }

      return keywords.map((keyword) => `<span class="block-item">${keyword}</span>`).join(" ");
    }

    // 渲染分类节点列表为带样式的项目
    function renderCategoryList(categories) {
      if (categories.length === 0) {
        return "<div style='font-style:italic;color:#999'>暂无屏蔽分类节点</div>";
      }

      return categories
        .map((category) => {
          return `<span class="block-item">
						${category}
					</span>`;
        })
        .join(" ");
    }

    // 显示当前屏蔽的用户名列表
    const userList = document.createElement("div");
    userList.className = "block-list";
    userList.innerHTML = `<h3 class="block-title">屏蔽的用户名 (显示最近30个):</h3>${renderUserList(blockedUsers)}`;

    // 创建用户名输入区域
    const userInputContainer = document.createElement("div");
    userInputContainer.className = "block-input-container";

    const userInput = document.createElement("input");
    userInput.type = "text";
    userInput.placeholder = "输入用户名";
    userInput.className = "block-input";

    const addUserButton = document.createElement("button");
    addUserButton.innerText = "添加用户名";
    addUserButton.className = "block-button";

    // 显示当前屏蔽的关键词列表
    const keywordList = document.createElement("div");
    keywordList.className = "block-list block-keyword-list";
    keywordList.innerHTML = `<h3 class="block-title">屏蔽的关键词:</h3>${renderKeywordList(keywords)}`;

    // 创建关键词输入区域
    const keywordInputContainer = document.createElement("div");
    keywordInputContainer.className = "block-input-container";

    const keywordInput = document.createElement("input");
    keywordInput.type = "text";
    keywordInput.placeholder = "输入关键词";
    keywordInput.className = "block-input";

    const addKeywordButton = document.createElement("button");
    addKeywordButton.innerText = "添加关键词";
    addKeywordButton.className = "block-button";

    // 添加用户名事件
    addUserButton.onclick = () => {
      const newUser = userInput.value.trim();
      if (newUser) {
        if (blockedUsers.includes(newUser)) {
          showNotification(`用户 "${newUser}" 已在屏蔽列表中`);
          return;
        }

        blockedUsers.push(newUser);
        if (safeSetValue("filterUsers", blockedUsers)) {
          userList.innerHTML = `<h3 class="block-title">屏蔽的用户名 (显示最近30个):</h3>${renderUserList(blockedUsers)}`;
          userInput.value = "";
          filterPosts();
          showNotification(`已添加屏蔽用户: ${newUser}`);
        }
      }
    };

    // 添加关键词事件
    addKeywordButton.onclick = () => {
      const newKeyword = keywordInput.value.trim();
      if (newKeyword) {
        // 检查是否已存在（不区分大小写）
        const exists = keywords.some((k) => k.toLowerCase() === newKeyword.toLowerCase());
        if (exists) {
          showNotification(`关键词 "${newKeyword}" 已在屏蔽列表中`);
          return;
        }

        keywords.push(newKeyword);
        if (safeSetValue("filterKeywords", keywords)) {
          keywordList.innerHTML = `<h3 class="block-title">屏蔽的关键词:</h3>${renderKeywordList(keywords)}`;
          keywordInput.value = "";
          filterPosts();
          showNotification(`已添加屏蔽关键词: ${newKeyword}`);
        }
      }
    };

    // 添加回车键提交
    userInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        addUserButton.click();
      }
    });

    keywordInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        addKeywordButton.click();
      }
    });

    // 组装界面元素
    userInputContainer.appendChild(userInput);
    userInputContainer.appendChild(addUserButton);
    keywordInputContainer.appendChild(keywordInput);
    keywordInputContainer.appendChild(addKeywordButton);

    // 显示当前屏蔽的分类节点列表
    const categoryList = document.createElement("div");
    categoryList.className = "block-list block-category-list";
    categoryList.innerHTML = `<h3 class="block-title">屏蔽的分类节点:</h3>${renderCategoryList(blockedCategories)}`;

    // 创建分类节点输入区域
    const categoryInputContainer = document.createElement("div");
    categoryInputContainer.className = "block-input-container";

    const categoryInput = document.createElement("input");
    categoryInput.type = "text";
    categoryInput.placeholder = "输入分类节点名称";
    categoryInput.className = "block-input";

    const addCategoryButton = document.createElement("button");
    addCategoryButton.innerText = "添加分类节点";
    addCategoryButton.className = "block-button";

    // 添加分类节点事件
    addCategoryButton.onclick = () => {
      const newCategory = categoryInput.value.trim();
      if (newCategory) {
        // 检查是否已存在（不区分大小写）
        const exists = blockedCategories.some((c) => c.toLowerCase() === newCategory.toLowerCase());
        if (exists) {
          showNotification(`分类节点 "${newCategory}" 已在屏蔽列表中`);
          return;
        }

        blockedCategories.push(newCategory);
        if (safeSetValue("filterCategories", blockedCategories)) {
          categoryList.innerHTML = `<h3 class="block-title">屏蔽的分类节点:</h3>${renderCategoryList(blockedCategories)}`;
          categoryInput.value = "";
          filterPosts();
          showNotification(`已添加屏蔽分类节点: ${newCategory}`);
        }
      }
    };

    // 添加回车键提交
    categoryInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        addCategoryButton.click();
      }
    });

    // 组装分类节点输入区域
    categoryInputContainer.appendChild(categoryInput);
    categoryInputContainer.appendChild(addCategoryButton);

    dialog.appendChild(titleBar);
    dialog.appendChild(closeButton);
    dialog.appendChild(userList);
    dialog.appendChild(userInputContainer);
    dialog.appendChild(keywordList);
    dialog.appendChild(keywordInputContainer);
    dialog.appendChild(categoryList);
    dialog.appendChild(categoryInputContainer);

    document.body.appendChild(dialog);
  }

  // 使用精确匹配检查用户是否被屏蔽
  // 直接使用 blockedUsers.includes(username) 进行精确匹配

  // 获取帖子的用户信息
  function getPostUsers(post, excludeUsersList = false) {
    const users = [];

    // console.log("获取帖子用户信息...");

    // 简化：只检查 data-user-card 属性
    // 如果 excludeUsersList 为 true，则排除 topic-map__users-list 中的用户
    let userCardLinks;
    if (excludeUsersList) {
      // 排除 topic-map__users-list 中的用户
      userCardLinks = Array.from(post.querySelectorAll("[data-user-card]")).filter((link) => {
        // 检查该元素是否在 topic-map__users-list 中
        return !link.closest(".topic-map__users-list");
      });
      // console.log(`找到 ${userCardLinks.length} 个用户卡片链接（已排除用户列表中的用户）`);
    } else {
      userCardLinks = post.querySelectorAll("[data-user-card]");
      // console.log(`找到 ${userCardLinks.length} 个用户卡片链接`);
    }

    userCardLinks.forEach((link) => {
      const username = link.getAttribute("data-user-card");
      if (username) {
        // console.log(`从[data-user-card]属性获取到用户: ${username}`);
        // 检查用户名是否在屏蔽列表中（精确匹配）
        // const isBlocked = blockedUsers.includes(username);
        // console.log(`用户 ${username} 是否在屏蔽列表中: ${isBlocked}`);

        users.push(username);
      }
    });

    // 去重并过滤空值
    const uniqueUsers = [...new Set(users)].filter((user) => user);
    // console.log(`最终获取到的用户: ${uniqueUsers.join(', ')}`);

    return uniqueUsers;
  }

  // 调试函数已移除

  // 检查文本是否包含关键词（不区分大小写，严格匹配）
  function containsKeywords(text, keywordList) {
    if (!text || !keywordList || keywordList.length === 0) return false;

    // 检查当前是否在详情页，如果是则不进行关键词匹配
    const isDetailPage = window.location.pathname.startsWith("/t/");
    if (isDetailPage) {
      // console.log("当前在详情页，跳过关键词匹配");
      return false;
    }

    const lowerText = text.toLowerCase();

    // 启用调试
    // console.log("检查文本:", lowerText);
    // console.log("关键词列表:", keywordList);

    return keywordList.some((keyword) => {
      // 转换关键词为小写
      const lowerKeyword = keyword.toLowerCase();

      // 检查关键词是否包含中文字符
      const containsChinese = /[\u4e00-\u9fa5]/.test(lowerKeyword);

      // 对中文和英文采用不同的匹配策略
      if (containsChinese) {
        // 对于中文：只要包含即可
        const chineseRegex = new RegExp(escapeRegExp(lowerKeyword), "i");
        const result = chineseRegex.test(lowerText);
        if (result) {
          // console.log(`匹配到中文关键词: ${keyword}`);
        }
        return result;
      } else {
        // 对于英文：使用词边界匹配
        const regex = new RegExp("\\b" + escapeRegExp(lowerKeyword) + "\\b", "i");
        const result = regex.test(lowerText);
        if (result) {
          // console.log(`匹配到英文关键词: ${keyword}`);
        }
        return result;
      }
    });
  }

  // 辅助函数：转义正则表达式中的特殊字符
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); // $&表示整个匹配的字符串
  }

  // 过滤帖子
  function filterPosts() {
    // 重新加载屏蔽列表，确保使用最新的数据
    reloadBlockLists();

    // 检查当前是否在主页
    const isHomePage = !window.location.pathname.startsWith("/t/");

    // 处理主页列表中的帖子 - 同时支持原始结构和美化后的结构
    const mainTopics = document.querySelectorAll(".topic-list-item");

    mainTopics.forEach((topic) => {
      // 获取标题文本，使用特定选择器只针对主页标题列表
      const titleElement = topic.querySelector(".main-link a.title");
      const title = titleElement ? titleElement.textContent.trim() : "";

      // 获取标签信息，检查是否包含敏感标签
      const tagElements = topic.querySelectorAll(".discourse-tag");
      const tags = Array.from(tagElements).map((tag) => tag.textContent.trim());
      const tagText = tags.join(" ");

      // 获取分类节点信息
      const categoryElement = topic.querySelector(".badge-category__name");
      const category = categoryElement ? categoryElement.textContent.trim() : "";

      // 获取作者信息 - 适应美化后的结构
      const users = getPostUsers(topic);

      // 检查标题是否包含关键词（不区分大小写）- 只在主页标题列表生效
      // 只有在主页时才检查关键词
      const hasKeyword = isHomePage && titleElement && (containsKeywords(title, keywords) || containsKeywords(tagText, keywords));

      // 检查是否包含屏蔽用户 - 确保检查所有可能的用户标识符
      // 只要有一个用户标识符在屏蔽列表中，就屏蔽该帖子
      // 添加详细调试信息
      // console.log(`主页列表检查用户是否被屏蔽: ${users.join(', ')}`);
      // console.log(`屏蔽用户列表: ${blockedUsers.slice(-10).join(', ')}...`); // 只显示最后10个用户，避免日志过长

      // 使用精确匹配检查是否包含屏蔽用户
      // console.log(`主页列表详细检查用户屏蔽状态 - 用户列表: ${JSON.stringify(users)}`);
      // console.log(`主页列表详细检查用户屏蔽状态 - 屏蔽列表: ${JSON.stringify(blockedUsers)}`);

      // 逐个检查每个用户是否在屏蔽列表中
      let blockedUserFound = null;
      for (const user of users) {
        const isBlocked = blockedUsers.includes(user);
        // console.log(`主页列表详细检查: 用户 ${user} 是否在屏蔽列表中: ${isBlocked}`);
        if (isBlocked) {
          blockedUserFound = user;
          break;
        }
      }

      const hasBlockedUser = !!blockedUserFound;
      if (hasBlockedUser) {
        // console.log(`主页列表找到被屏蔽的用户: ${blockedUserFound}`);
      } else {
        // console.log(`主页列表没有找到被屏蔽的用户`);
      }

      // 检查是否属于屏蔽的分类节点
      const hasBlockedCategory = category && blockedCategories.some((blockedCat) => category.startsWith(blockedCat));

      if (hasKeyword || hasBlockedUser || hasBlockedCategory) {
        console.log(`🚫 [DEBUG] 屏蔽主页帖子: ${title}`, {
          hasKeyword,
          hasBlockedUser,
          hasBlockedCategory,
          blockedUserFound,
          users,
          category,
        });
        topic.classList.add("filtered-topic");
        // 确保样式被应用
        topic.style.display = "none !important";
        // 如果是美化后的结构，可能需要设置更高的优先级
        if (topic.style.display !== "none") {
          topic.setAttribute("style", "display: none !important");
        }
      } else {
        // 检查是否之前被屏蔽过，如果是则记录恢复日志
        if (topic.classList.contains("filtered-topic")) {
          console.log(`✅ [DEBUG] 恢复显示主页帖子: ${title}`);
        }
        topic.classList.remove("filtered-topic");
        // 移除可能添加的内联样式
        if (topic.style.display === "none") {
          topic.style.removeProperty("display");
        }
      }
    });

    // 处理详情页中的帖子和回复
    const detailPosts = document.querySelectorAll(".topic-post");

    detailPosts.forEach((post) => {
      // 获取所有可能的用户标识符，排除用户列表中的用户
      const users = getPostUsers(post, true); // 传入 true 表示排除用户列表中的用户

      // 获取分类节点信息（在详情页可能需要从其他元素获取）
      const categoryElement = document.querySelector(".badge-category__name");
      const category = categoryElement ? categoryElement.textContent.trim() : "";

      // 检查是否包含屏蔽用户 - 修改为检查所有用户标识符
      // 只要有一个用户标识符在屏蔽列表中，就屏蔽该帖子
      // 添加详细调试信息
      // console.log(`检查用户是否被屏蔽: ${users.join(', ')}`);
      // console.log(`屏蔽用户列表: ${blockedUsers.slice(-10).join(', ')}...`); // 只显示最后10个用户，避免日志过长

      // 使用精确匹配检查是否包含屏蔽用户
      // console.log(`详细检查用户屏蔽状态 - 用户列表: ${JSON.stringify(users)}`);
      // console.log(`详细检查用户屏蔽状态 - 屏蔽列表: ${JSON.stringify(blockedUsers)}`);

      // 逐个检查每个用户是否在屏蔽列表中
      let blockedUserFound = null;
      for (const user of users) {
        const isBlocked = blockedUsers.includes(user);
        // console.log(`详细检查: 用户 ${user} 是否在屏蔽列表中: ${isBlocked}`);
        if (isBlocked) {
          blockedUserFound = user;
          break;
        }
      }

      const hasBlockedUser = !!blockedUserFound;
      if (hasBlockedUser) {
        // console.log(`找到被屏蔽的用户: ${blockedUserFound}`);
      } else {
        // console.log(`没有找到被屏蔽的用户`);
      }

      // 检查是否属于屏蔽的分类节点
      const hasBlockedCategory = category && blockedCategories.some((blockedCat) => category.startsWith(blockedCat));

      // 注意：详情页中完全不检查关键词，关键词屏蔽只对主页标题列表生效
      // 确保在详情页中不会因为关键词而屏蔽帖子

      // 简化屏蔽逻辑：直接检查是否需要屏蔽
      if (hasBlockedUser || hasBlockedCategory) {
        console.log(`� [DEBUG] 屏蔽详情页帖子`, {
          postElement: post,
          hasBlockedUser,
          hasBlockedCategory,
          blockedUserFound,
          users,
          category,
        });

        // 使用终极屏蔽方法：直接移除DOM元素
        console.log(`💀 [DEBUG] 使用终极屏蔽方法 - 移除DOM元素`);
        post.remove();
      }
      // 不需要屏蔽的帖子保持原样
    });

    const endTime = performance.now();
    console.log(`⏱️ [DEBUG] 过滤帖子完成`, {
      duration: `${(endTime - startTime).toFixed(2)}ms`,
      mainTopicsCount: mainTopics.length,
      detailPostsCount: detailPosts.length,
      isHomePage,
      blockedUsersCount: blockedUsers.length,
      keywordsCount: keywords.length,
      blockedCategoriesCount: blockedCategories.length,
    });
  }

  // 在头像旁添加屏蔽按钮
  function addBlockUserButtonsToAvatars() {
    console.log("🔧 [DEBUG] 尝试在头像旁添加屏蔽按钮");

    // 查找所有头像元素 - 扩展选择器以覆盖更多可能的头像元素
    const avatarElements = document.querySelectorAll(`
      .topic-avatar img,
      .avatar img,
      .topic-list-item .poster img,
      .topic-list .posters a img,
      .latest-topic-list .posters a img,
      td.posters a img,
      .user-image img,
      .topic-post .avatar img,
      .post-avatar img,
      .user-avatar img,
      a[data-user-card] img,
      .topic-body .avatar img
    `);

    console.log(`🔧 [DEBUG] 找到 ${avatarElements.length} 个头像元素`);

    // 处理每个头像元素
    avatarElements.forEach((avatarImg, index) => {
      // 跳过已处理的头像
      if (avatarImg.dataset.blockButtonAdded === "true") return;

      console.log(`🔧 [DEBUG] 处理头像元素 ${index + 1}/${avatarElements.length}`);

      // 获取用户名 - 处理不同的页面结构
      let username = "";

      // 1. 检查头像本身是否在链接内，链接上有data-user-card属性
      const parentLink = avatarImg.closest("a[data-user-card]");
      if (parentLink) {
        username = parentLink.getAttribute("data-user-card");
        console.log(`🔧 [DEBUG] 从父链接获取用户名: ${username}`);
      }

      // 2. 如果头像上没有找到，则查找包含头像的帖子元素
      if (!username) {
        const postElement = avatarImg.closest(".topic-post") || avatarImg.closest(".topic-list-item");
        if (postElement) {
          const userCardElement = postElement.querySelector("[data-user-card]");
          if (userCardElement) {
            username = userCardElement.getAttribute("data-user-card");
            console.log(`🔧 [DEBUG] 从帖子元素获取用户名: ${username}`);
          }
        }
      }

      // 3. 对于主页的特殊结构，可能需要查找父级元素中的用户名
      if (!username && avatarImg.closest("td.posters")) {
        const posterCell = avatarImg.closest("td.posters");
        const userLink = posterCell.querySelector("a[data-user-card]");
        if (userLink) {
          username = userLink.getAttribute("data-user-card");
          console.log(`🔧 [DEBUG] 从海报单元格获取用户名: ${username}`);
        }
      }

      // 4. 尝试从头像的alt属性或title属性获取用户名
      if (!username) {
        username = avatarImg.alt || avatarImg.title;
        if (username) {
          console.log(`🔧 [DEBUG] 从头像属性获取用户名: ${username}`);
        }
      }

      // 如果所有方法都无法找到用户名，则跳过
      if (!username) {
        console.log(`🔧 [DEBUG] 无法获取用户名，跳过此头像`);
        return;
      }

      // 标记头像已处理
      avatarImg.dataset.blockButtonAdded = "true";

      // 检查用户是否已被屏蔽，如果已屏蔽则不添加按钮
      if (blockedUsers.includes(username)) {
        console.log(`🔧 [DEBUG] 用户 ${username} 已被屏蔽，跳过添加按钮`);
        return;
      }

      // 检查是否已经添加了按钮容器
      let avatarContainer = avatarImg.closest(".avatar-container");
      if (!avatarContainer) {
        console.log(`🔧 [DEBUG] 为用户 ${username} 创建头像容器`);
        // 创建容器
        avatarContainer = document.createElement("div");
        avatarContainer.className = "avatar-container";

        // 将头像包裹在容器中
        const parent = avatarImg.parentNode;
        parent.insertBefore(avatarContainer, avatarImg);
        avatarContainer.appendChild(avatarImg);
      } else {
        console.log(`🔧 [DEBUG] 用户 ${username} 已有头像容器`);
      }

      // 检查是否已经添加了屏蔽按钮
      if (avatarContainer.querySelector(".block-avatar-hover-button")) {
        console.log(`🔧 [DEBUG] 用户 ${username} 已有屏蔽按钮，跳过`);
        return;
      }

      // 创建屏蔽按钮
      console.log(`🔧 [DEBUG] 为用户 ${username} 创建屏蔽按钮`);
      const blockButton = document.createElement("span");
      blockButton.textContent = "屏蔽用户";
      blockButton.className = "block-avatar-hover-button";
      blockButton.title = `屏蔽用户: ${username}`;

      // 确保按钮样式正确应用
      blockButton.style.cssText = `
        display: none !important;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        padding: 4px 8px;
        font-size: 11px;
        background-color: rgba(0, 0, 0, 0.9);
        color: #fff;
        border-radius: 6px;
        cursor: pointer;
        z-index: 1000;
        box-shadow: 0px 2px 4px rgba(0,0,0,0.3);
        white-space: nowrap;
        opacity: 0;
        bottom: -32px;
        transition: opacity 0.2s ease, transform 0.1s ease;
        pointer-events: auto;
      `;

      // 添加点击事件
      blockButton.addEventListener("click", (e) => {
        e.preventDefault();
        e.stopPropagation();

        // 检查用户是否已被屏蔽
        if (blockedUsers.includes(username)) {
          showNotification(`用户 "${username}" 已在屏蔽列表中`);
          return;
        }

        // 添加用户到屏蔽列表
        blockedUsers.push(username);
        if (safeSetValue("filterUsers", blockedUsers)) {
          filterPosts(); // 重新过滤帖子
          showNotification(`已添加屏蔽用户: ${username}`);

          // 添加已屏蔽标记
          const indicator = document.createElement("span");
          indicator.textContent = " (已屏蔽)";
          indicator.className = "blocked-user-indicator";
          indicator.style.color = "grey";
          indicator.style.fontSize = "0.9em";
          indicator.style.marginLeft = "4px";

          // 查找用户名元素以添加标记
          let userNameElement = null;

          // 1. 首先尝试使用之前找到的parentLink
          if (parentLink) {
            userNameElement = parentLink;
          }

          // 2. 如果没有parentLink，尝试在帖子元素中查找
          if (!userNameElement) {
            const postElement = avatarImg.closest(".topic-post") || avatarImg.closest(".topic-list-item");
            if (postElement) {
              userNameElement = postElement.querySelector("[data-user-card]");
            }
          }

          // 3. 对于主页的特殊结构
          if (!userNameElement && avatarImg.closest("td.posters")) {
            const posterCell = avatarImg.closest("td.posters");
            userNameElement = posterCell.querySelector("a[data-user-card]");
          }

          // 将标记添加到用户名旁
          if (userNameElement) {
            if (userNameElement.nextSibling) {
              userNameElement.parentNode.insertBefore(indicator, userNameElement.nextSibling);
            } else {
              userNameElement.parentNode.appendChild(indicator);
            }
          }

          // 移除屏蔽按钮
          blockButton.remove();

          // 移除头像容器的特殊样式，恢复正常显示
          if (avatarContainer) {
            avatarContainer.style.marginBottom = "0";
            // 创建一个新的普通容器替换avatar-container
            const newContainer = document.createElement("div");
            const parent = avatarContainer.parentNode;
            while (avatarContainer.firstChild) {
              newContainer.appendChild(avatarContainer.firstChild);
            }
            parent.replaceChild(newContainer, avatarContainer);
          }
        }
      });

      // 将按钮添加到容器
      avatarContainer.appendChild(blockButton);
    });
  }

  // 添加事件监听器
  function addEventListeners() {
    // 添加事件监听器到屏蔽按钮
    const blockListButton = document.querySelector(".block-list-button");
    if (blockListButton) {
      blockListButton.addEventListener("click", showBlockListDialog);
    }
  }

  // 渲染UI
  function renderUI() {
    createBlockButton();
  }

  // #endregion
})();
